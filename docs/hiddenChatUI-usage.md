# HiddenChatUI 访问使用说明

## 概述

现在你可以在 `injectTranslate.ts` 中访问 `HiddenChatUI` 的 ref 实例了。`WebAssistantManager` 实例已经挂载到 `window.webAssistantManager` 上，提供了多种访问方式。

## 可用的访问函数

### 1. `isHiddenChatUIAvailable(): boolean`

检查 HiddenChatUI 是否可用。

```typescript
if (isHiddenChatUIAvailable()) {
  console.log('HiddenChatUI 可用')
} else {
  console.log('HiddenChatUI 不可用')
}
```

### 2. `getHiddenChatUIRef(): HiddenChatUIRef | null`

获取 HiddenChatUI 的 ref 实例，可以直接访问其属性和方法。

```typescript
const chatUIRef = getHiddenChatUIRef()
if (chatUIRef) {
  console.log('ChatContext:', chatUIRef.chatContext)
  // 直接调用 onSend 方法
  const result = await chatUIRef.onSend('text', '你好', { agentId: 'translate' })
}
```

### 3. `callHiddenChatUI(type, content, options?, attachments?): Promise<any>`

封装好的调用函数，推荐使用这个方法。

```typescript
try {
  const result = await callHiddenChatUI(
    'text',
    '翻译这段文本：Hello World',
    { agentId: 'translate' }
  )
  console.log('翻译结果:', result)
} catch (error) {
  console.error('调用失败:', error)
}
```

## 使用示例

### 基本翻译调用

```typescript
async function translateText(text: string) {
  try {
    // 检查可用性
    if (!isHiddenChatUIAvailable()) {
      throw new Error('HiddenChatUI 不可用')
    }
    
    // 调用翻译
    const result = await callHiddenChatUI(
      'text',
      `翻译：${text}`,
      { agentId: 'translate' }
    )
    
    return result
  } catch (error) {
    console.error('翻译失败:', error)
    throw error
  }
}
```

### 页面总结调用

```typescript
async function summarizePage() {
  try {
    const result = await callHiddenChatUI(
      'text',
      '总结当前页面内容',
      { agentId: 'summary' }
    )
    
    return result
  } catch (error) {
    console.error('总结失败:', error)
    throw error
  }
}
```

### 直接访问 ChatContext

```typescript
function getChatContext() {
  const chatUIRef = getHiddenChatUIRef()
  if (chatUIRef?.chatContext) {
    return chatUIRef.chatContext
  }
  return null
}
```

## 测试功能

在开发环境中，已经自动加载了测试脚本。你可以在浏览器控制台中运行：

```javascript
// 运行完整测试
window.testHiddenChatUI()
```

这将测试所有的访问功能并输出详细的日志。

## 注意事项

1. **初始化时机**：确保在 `WebAssistantManager` 初始化完成后再调用这些函数。通常在页面加载完成后稍等一段时间（如 1 秒）。

2. **错误处理**：始终使用 try-catch 包装异步调用，因为网络请求可能失败。

3. **类型安全**：虽然使用了 `any` 类型避免循环依赖，但在实际使用中这些函数是类型安全的。

4. **性能考虑**：`HiddenChatUI` 是隐藏的，不会影响页面性能，但网络请求仍然会消耗资源。

## 架构说明

```
injectTranslate.ts
    ↓ (通过 window.webAssistantManager)
WebAssistantManager
    ↓ (管理)
HiddenChatUI (React 组件)
    ↓ (包含)
ChatUI (实际的聊天组件)
```

现在你可以在 `injectTranslate.ts` 中直接调用 `HiddenChatUI` 的功能，无需通过复杂的消息传递机制。
