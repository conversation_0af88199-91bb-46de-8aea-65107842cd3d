/**
 * 测试 HiddenChatUI 访问功能
 * 这个文件用于测试在 injectTranslate.ts 中访问 HiddenChatUI 的功能
 */

import { 
  getHiddenChatUIRef, 
  callHiddenChatUI, 
  isHiddenChatUIAvailable,
  exampleUseHiddenChatUI
} from './injectTranslate'

/**
 * 测试 HiddenChatUI 的各种访问方法
 */
export async function testHiddenChatUIAccess() {
  console.log('=== 开始测试 HiddenChatUI 访问功能 ===')
  
  // 等待一段时间确保 WebAssistantManager 已初始化
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 测试 1: 检查可用性
  console.log('测试 1: 检查 HiddenChatUI 可用性')
  const isAvailable = isHiddenChatUIAvailable()
  console.log('HiddenChatUI 可用:', isAvailable)
  
  if (!isAvailable) {
    console.warn('HiddenChatUI 不可用，跳过后续测试')
    return
  }
  
  // 测试 2: 获取 ref 实例
  console.log('测试 2: 获取 HiddenChatUI ref 实例')
  const chatUIRef = getHiddenChatUIRef()
  console.log('获取到的 ref:', chatUIRef)
  console.log('ref 的 chatContext:', chatUIRef?.chatContext)
  
  // 测试 3: 直接调用 onSend 方法
  console.log('测试 3: 直接调用 HiddenChatUI 的 onSend 方法')
  try {
    if (chatUIRef?.onSend) {
      const result = await chatUIRef.onSend(
        'text',
        '测试消息：请回复"测试成功"',
        { agentId: 'translate' }
      )
      console.log('直接调用结果:', result)
    } else {
      console.warn('onSend 方法不可用')
    }
  } catch (error) {
    console.error('直接调用失败:', error)
  }
  
  // 测试 4: 通过封装函数调用
  console.log('测试 4: 通过封装函数调用 HiddenChatUI')
  try {
    const result = await callHiddenChatUI(
      'text',
      '测试消息：翻译 "Hello World"',
      { agentId: 'translate' }
    )
    console.log('封装函数调用结果:', result)
  } catch (error) {
    console.error('封装函数调用失败:', error)
  }
  
  // 测试 5: 运行示例函数
  console.log('测试 5: 运行示例函数')
  try {
    const result = await exampleUseHiddenChatUI()
    console.log('示例函数结果:', result)
  } catch (error) {
    console.error('示例函数失败:', error)
  }
  
  console.log('=== HiddenChatUI 访问功能测试完成 ===')
}

/**
 * 测试直接访问 window.webAssistantManager
 */
export function testDirectWindowAccess() {
  console.log('=== 测试直接访问 window.webAssistantManager ===')
  
  const manager = (window as any).webAssistantManager
  console.log('window.webAssistantManager:', manager)
  
  if (manager) {
    console.log('Manager 方法列表:')
    console.log('- getHiddenChatUIRef:', typeof manager.getHiddenChatUIRef)
    console.log('- callHiddenChatUI:', typeof manager.callHiddenChatUI)
    
    const ref = manager.getHiddenChatUIRef()
    console.log('直接获取的 ref:', ref)
    console.log('ref.chatContext:', ref?.chatContext)
    console.log('ref.onSend:', typeof ref?.onSend)
  } else {
    console.warn('window.webAssistantManager 不存在')
  }
  
  console.log('=== 直接访问测试完成 ===')
}

/**
 * 在控制台中运行测试
 * 使用方法：在浏览器控制台中输入 window.testHiddenChatUI()
 */
export function attachTestToWindow() {
  (window as any).testHiddenChatUI = testHiddenChatUIAccess;
  (window as any).testDirectAccess = testDirectWindowAccess
  console.log('测试函数已挂载到 window:')
  console.log('- window.testHiddenChatUI() - 完整功能测试')
  console.log('- window.testDirectAccess() - 直接访问测试')
}

// 自动挂载测试函数到 window
if (typeof window !== 'undefined') {
  attachTestToWindow()
}
