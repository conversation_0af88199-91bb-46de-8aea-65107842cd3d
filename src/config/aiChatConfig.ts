import { handleTranslateCurrentPage, handleSummaryCurrentPage } from '../sidepanel/components/AiChat/utils'

const skill = [
  {
    key: '',// 必传，唯一标识
    disabled: false,//是否禁用
    icon: '',// 图标
    label: '智能翻译',// 文案
    question: '翻译上面文字',// 发送时的命令
    agentId: '1',//发送时可指定智能体ID
    children: [
      {
        key: 'translate',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: 'AI翻译',// 文案
        question: 'AI翻译',// 发送时的命令
        agentId: 'translate',//发送时可指定智能体ID
        // children?: Skill[],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
      },
      {
        key: 'translate-page',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译此页面',// 文案
        question: '翻译此页面',// 发送时的命令
        agentId: 'translate-page',//发送时可指定智能体ID
        // children?: Skill[],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => handleTranslateCurrentPage(),// 技能点击回调 -》 由AiChat统一写入Composer
      }
    ],//折叠子项,有子项时，点击会展开子项
    // expandIcon?: string,// 折叠图标
    // customRender?: React.ReactNode,//自定义渲染
    onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
  },
  {
    key: 'summary',// 必传，唯一标识
    disabled: false,//是否禁用
    icon: '',// 图标
    label: '网页总结',// 文案
    question: '网页总结',// 发送时的命令
    agentId: 'summary',//发送时可指定智能体ID
    // children?: Skill[],//折叠子项,有子项时，点击会展开子项
    // expandIcon?: string,// 折叠图标
    // customRender?: React.ReactNode,//自定义渲染
    onClick: () => handleSummaryCurrentPage(),// 技能点击回调 -》 由AiChat统一写入Composer
  },
  {
    key: 'more',// 必传，唯一标识
    disabled: false,//是否禁用
    icon: '',// 图标
    label: '更多',// 文案
    question: '翻译上面文字',// 发送时的命令
    agentId: '1',//发送时可指定智能体ID
    children: [
      {
        key: 'default',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '图片文字抓取',// 文案
        question: '翻译上面文字',// 发送时的命令
        agentId: 'default',//发送时可指定智能体ID
        // children?: Skill[],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
      },
      {
        key: 'text-condenser',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '缩写',// 文案
        question: '缩写',// 发送时的命令
        agentId: 'text-condenser',//发送时可指定智能体ID
        // children?: Skill[],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
      },
      {
        key: 'text-expander',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '扩写',// 文案
        question: '扩写',// 发送时的命令
        agentId: 'text-expander',//发送时可指定智能体ID
        // children?: Skill[],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
      },
      {
        key: 'text-polisher',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '润色',// 文案
        question: '润色',// 发送时的命令
        agentId: 'text-polisher',//发送时可指定智能体ID
        // children?: Skill[],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
      },
      {
        key: 'grammar-corrector',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '修正拼写和语法',// 文案
        question: '修正拼写和语法',// 发送时的命令
        agentId: 'grammar-corrector',//发送时可指定智能体ID
        // children?: Skill[],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
      }
    ]
  }
]

const composerConfig = {
  // aboveNode: <AboveNode chatUiRef={chatUiRef} />,
  // placeholder: getPlaceholder(),
  quoteOperations: {
    citetext: [{
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '翻译',// 文案
      question: '翻译',// 发送时的命令 --》 拼装
      agentId: 'translate',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    },
    {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '总结',// 文案
      question: '总结',// 发送时的命令 --》 拼装
      agentId: 'summary',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }, {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '扩写',// 文案
      question: '扩写',// 发送时的命令 --》 拼装
      agentId: 'text-expander',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }, {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '缩写',// 文案
      question: '缩写',// 发送时的命令 --》 拼装
      agentId: 'abbreviation',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }, {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '润色',// 文案
      question: '润色',// 发送时的命令 --》 拼装
      agentId: 'text-polisher',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }, {
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '修正拼写和语法',// 文案
      question: '修正拼写和语法',// 发送时的命令 --》 拼装
      agentId: 'grammar-corrector',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }],
    citeweb: [{
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '翻译',// 文案
      question: '翻译翻译',// 发送时的命令 --》 拼装
      agentId: 'agent1',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }],

    image: [
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译翻译',// 发送时的命令 --》 拼装
        agentId: 'agent1',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }
    ],

    file: [{
      disabled: false,//是否禁用
      icon: '',// 图标
      label: '翻译',// 文案
      question: '翻译翻译',// 发送时的命令 --》 拼装
      agentId: 'agent1',//发送时可指定智能体ID --》拼装
      customRender: '',//自定义渲染
    }],
  },
  skill,

}
const config = {
  //租户id：表示当前系统
  appId: 'web-assistant',

  //用户id：代表当前系统唯一用户id
  userId: 'anonymous_user',

  requests: {
    baseUrl() {
      return ``
    },
    init: {
      // type: 'http',//请求链路类型：'tcp'/'http',如果是tcp则需要传aciton字段，默认http
      // action:'27006',//移动端如果走tcp，需要传接口请求action号
      // paramsKey：'MS__REQUEST__PAYLOAD'，//移动端如果走tcp，app包裹参数，不通app不一样，聊他传‘MS__REQUEST__PAYLOAD’
      url: 'http://10.102.92.209:9607/ai/orchestration/session/createSession',
      // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',

      headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },//请求header，可选
      // requestTransfer: (input: object) => { //同步写法
      // requestTransfer:async (input: object) => { //支持异步写法
      //   const parsedInput = {
      //     ...input,
      //     customerInput1: '123',
      //   }
      //   return parsedInput;
      // },
      // responseTransfer: (output: object) => {
      //   const parsedOutput = {
      //     ...output,
      //     customeroutput1: '123',
      //   }
      //   return parsedOutput;
      // },
    },


    //问答接口
    send: {
      url: 'http://webassist.sit.sass.htsc/chat/workflow/chrome',
      createConversationUrl: 'http://10.102.92.209:9607/ai/orchestration/session/createSession',
      isAibag: true,
      stream: true,
      messageInterval: 50,
      // async requestTransfer(params) {
      //   if (isPagesummary) {
      //     params.inputs = {
      //       HTML_CONTENT: await getPageContent(),
      //     }
      //   }
      //   return params
      // },
    },
    //查询历史详情接口
    history: {
      url: 'http://10.102.92.209:9607/ai/orchestration/session/getHistoryMessages',
    },
    //点赞点踩接口
    score: {
      url: 'http://10.102.92.209:9607/ai/orchestration/session/feedback',
    },
    //停止生成接口
    stop: {
      url: 'http://10.102.92.209:9607/ai/orchestration/session/interruptSession',
    },

    // 历史会话列表
    historyConversation: {
      url: 'http://10.102.92.209:9607/ai/orchestration/session/getHistorySessions',
    },

    deleteHistoryConversation: {
      url: 'http://10.102.92.209:9607/ai/orchestration/session/deleteSession',
    },

    deleteMessage: {
      url: 'http://10.102.92.209:9607/ai/orchestration/session/deleteMessage',
    },

    // 创建新会话接口
    createConversation: {
      url: 'http://10.102.92.209:9607/ai/orchestration/session/createSession',
    },

  },
}

export {
  composerConfig,
  config
};
