import '@ht/chatui/dist/index.css'

import React, { useRef, lazy, Suspense, useEffect } from 'react'
import { init, log } from '@ht/xlog'
import { isPrd } from '@src/common/utils'
import { message } from 'antd/es'
import { getCurrentTab } from '@src/sidepanel/utils'
import GuidePage from './GuidePage'
import FooterVersion from './FooterVersion'
import Loading from './Loading'
import HistoryListItem from './HistoryListItem'
import ChatUI from '@ht/chatui'
import { MessageType, EFloatButtonActionType } from '@src/common/const'
import { getAllActions } from '@src/common/actions'
import { composerConfig, config } from './aiChatConfig'
import { handleTranslateCurrentPage, handleSummaryCurrentPage } from './utils'
import './style.less'


interface AiChatProps {
  selectedText?: string;
  textOperation?: string;
}

export default ({ selectedText, textOperation }: AiChatProps) => {
  const chatUiRef = useRef(null)
  const [messageApi, contextHolder] = message.useMessage()
  const actions = getAllActions()
  // 初始化日志
  const initLog = () => {
    init({
      uuid: 'anonymous_user',
      from: 'HtscAiExtension',
      types: ['fetch', 'unhandledrejection', 'windowError'],
      myTrackConfig: {
        // 项目信息配置，数据是上报到数智中台，需要去数智中台申请一个项目（product_id和product_name）
        product_id: '366',
        product_name: 'Web开发平台',
        channel_env: isPrd ? 'prd_outer' : 'prd_outer_test', // 上报环境
      },
    })
  }

  useEffect(() => {
    initLog()
  }, [])


  useEffect(() => {
    // 监听来自背景脚本的消息
    const messageListener = (message: {
      type: EFloatButtonActionType
    }) => {
      console.log('Sidepanel received message:', message);
      if (message.type === EFloatButtonActionType.Summary) {
        handleSummaryCurrentPage(messageApi, chatUiRef)
      }
      //  else if (message.type === EFloatButtonActionType.Translate) {
      //   handleTranslateCurrentPage(messageApi)
      // }
    }; chrome.runtime.onMessage.addListener(messageListener);

    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, []);

  type TRenderWelcomeReturnType = React.ReactNode &
    React.ForwardRefExoticComponent<any>

  const onReportLog = (params) => {
    const { id, page_id, page_title, btn_id, btn_title } = params
    if (id) {
      log({
        id,
        page_id,
        page_title,
        btn_id,
        btn_title,
      })
    }
  }






  return (
    <Suspense fallback={<Loading />}>
      <ChatUI
        navbar={{
          showLogo: false,
          showCloseButton: false,
          title: '',
        }}
        ref={chatUiRef}
        config={config}
        actions={actions}
        // historyConversation={{
        //   renderListItem(props) {
        //     return <HistoryListItem {...props} />
        //   },
        // }}
        renderWelcome={(props) =>
          (<GuidePage {...props} />) as TRenderWelcomeReturnType
        }
        onReportLog={onReportLog}
        inputOptions={{
          minRows: 2,
        }}
        composerConfig={composerConfig}
        renderFooterVersion={() => <FooterVersion />}
        showStopAnswer={true}
        showToken={false} // 不展示消耗的token数量
        showHallucination={false} // 不展示合规话术
      />
    </Suspense>
  )
}
